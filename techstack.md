# Tech Stack

## Core Framework
- **React 18** + **TypeScript** - Modern component-based UI
- **Vite** - Fast build tool with SWC compiler

## Styling & UI
- **Tailwind CSS** - Utility-first styling
- **shadcn/ui** - Premium component library
- **Radix UI** - Accessible primitives

## State & Forms
- **React Hook Form** - Performant form handling
- **Zod** - Runtime validation
- **TanStack Query** - Server state management

## Routing & Navigation
- **React Router DOM** - Client-side routing

## Development Tools
- **ESLint** - Code linting
- **PostCSS** + **Autoprefixer** - CSS processing
- **Lovable Tagger** - Component development

## Package Management
- **npm** - Primary package manager
- **Bun** - Alternative runtime (lockfile present)

## Key Libraries
- **Lucide React** - Icon system
- **date-fns** - Date utilities
- **Recharts** - Data visualization
- **Sonner** - Toast notifications
